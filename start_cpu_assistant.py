#!/usr/bin/env python3
"""
CPU Assistant Launcher - Start continuous CPU assistance as background service
"""

import os
import sys
import time
import subprocess
import signal
import psutil
from datetime import datetime

def check_if_running():
    """Check if CPU assistant is already running."""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'])
                if 'ump.py --continuous' in cmdline:
                    return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def start_assistant():
    """Start CPU assistant in background."""
    existing_pid = check_if_running()
    if existing_pid:
        print(f"CPU Assistant already running (PID: {existing_pid})")
        return existing_pid

    print("Starting CPU Assistant...")
    
    # Start as background process
    if os.name == 'nt':  # Windows
        # Use CREATE_NEW_PROCESS_GROUP to allow independent execution
        proc = subprocess.Popen(
            [sys.executable, 'ump.py', '--continuous'],
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
    else:  # Unix-like
        proc = subprocess.Popen(
            [sys.executable, 'ump.py', '--continuous'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
        )
    
    # Wait a moment to ensure it started
    time.sleep(2)
    
    if proc.poll() is None:  # Still running
        print(f"CPU Assistant started successfully (PID: {proc.pid})")
        print(f"   Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Process ID: {proc.pid}")
        return proc.pid
    else:
        print("Failed to start CPU Assistant")
        stdout, stderr = proc.communicate()
        if stderr:
            print(f"Error: {stderr.decode()}")
        return None

def stop_assistant():
    """Stop running CPU assistant."""
    pid = check_if_running()
    if not pid:
        print("No CPU Assistant running")
        return False

    try:
        print(f"Stopping CPU Assistant (PID: {pid})...")
        
        if os.name == 'nt':  # Windows
            # Send Ctrl+C signal
            os.kill(pid, signal.CTRL_C_EVENT)
        else:  # Unix-like
            os.kill(pid, signal.SIGTERM)
        
        # Wait for graceful shutdown
        for i in range(10):
            if not check_if_running():
                print("CPU Assistant stopped gracefully")
                return True
            time.sleep(0.5)

        # Force kill if still running
        try:
            os.kill(pid, signal.SIGKILL if os.name != 'nt' else signal.SIGTERM)
            print("CPU Assistant force-stopped")
            return True
        except ProcessLookupError:
            print("CPU Assistant stopped")
            return True

    except Exception as e:
        print(f"Error stopping CPU Assistant: {e}")
        return False

def status_assistant():
    """Show CPU assistant status."""
    pid = check_if_running()
    if not pid:
        print("CPU Assistant is NOT running")
        return
    
    try:
        proc = psutil.Process(pid)
        create_time = datetime.fromtimestamp(proc.create_time())
        uptime = datetime.now() - create_time
        
        print(f"CPU Assistant is RUNNING")
        print(f"   Process ID: {pid}")
        print(f"   Started: {create_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   Uptime: {str(uptime).split('.')[0]}")
        print(f"   CPU usage: {proc.cpu_percent():.1f}%")
        print(f"   Memory: {proc.memory_info().rss / 1024 / 1024:.1f} MB")
        print(f"   Threads: {proc.num_threads()}")
        
        # Check for workspace files
        workspace_files = []
        for file in os.listdir('.'):
            if file.startswith('ump_cpu_assistant_') and file.endswith('.workspace'):
                size_mb = os.path.getsize(file) / 1024 / 1024
                workspace_files.append(f"{file} ({size_mb:.1f}MB)")
        
        if workspace_files:
            print(f"   Workspace files: {', '.join(workspace_files)}")
        
    except Exception as e:
        print(f"Error getting status: {e}")

def main():
    """Main launcher function."""
    if len(sys.argv) < 2:
        print("CPU Assistant Launcher")
        print("Usage:")
        print("  python start_cpu_assistant.py start   - Start CPU assistant")
        print("  python start_cpu_assistant.py stop    - Stop CPU assistant")
        print("  python start_cpu_assistant.py status  - Show status")
        print("  python start_cpu_assistant.py restart - Restart CPU assistant")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'start':
        start_assistant()
    elif command == 'stop':
        stop_assistant()
    elif command == 'status':
        status_assistant()
    elif command == 'restart':
        print("Restarting CPU Assistant...")
        stop_assistant()
        time.sleep(2)
        start_assistant()
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()
