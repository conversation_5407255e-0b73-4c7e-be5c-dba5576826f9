#!/usr/bin/env python3
"""
Simple test script for Universal Mathematical Processor
"""

import numpy as np
import sys

# Import from our ump.py file
exec(open('ump.py', encoding='utf-8').read())

def test_basic_functionality():
    """Test basic UMP functionality."""
    print("=== Testing Basic UMP Functionality ===")
    
    # Test 1: NodeGrid creation
    print("\n1. Testing NodeGrid creation...")
    grid = NodeGrid(16, 16, alpha=0.9, beta=0.5, gamma=0.1, theta=0.0)
    print(f"   ✓ NodeGrid created with shape: {grid.state.shape}")
    print(f"   ✓ Parameters: α={grid.alpha}, β={grid.beta}, γ={grid.gamma}, θ={grid.theta}")
    
    # Test 2: Bit conversion
    print("\n2. Testing bit conversion...")
    # Set some known values
    grid.state[:4, :4] = 1.0   # Should become 1s
    grid.state[4:8, :4] = -1.0 # Should become 0s
    bits = grid.to_bits()
    expected_ones = np.sum(bits[:4, :4])
    expected_zeros = np.sum(bits[4:8, :4])
    print(f"   ✓ Positive values → bits: {expected_ones}/16 ones (expected: 16)")
    print(f"   ✓ Negative values → bits: {expected_zeros}/16 ones (expected: 0)")
    
    # Test 3: State initialization from bits
    print("\n3. Testing state initialization from bits...")
    test_bits = np.array([[1, 0, 1, 0],
                         [0, 1, 0, 1],
                         [1, 1, 0, 0],
                         [0, 0, 1, 1]], dtype=np.uint8)
    grid_small = NodeGrid(4, 4)
    grid_small.set_state_from_bits(test_bits)
    reconstructed_bits = grid_small.to_bits()
    accuracy_val = np.sum(test_bits == reconstructed_bits) / test_bits.size
    print(f"   ✓ Bit reconstruction accuracy: {accuracy_val:.3f} (expected: 1.0)")
    
    # Test 4: Single step processing
    print("\n4. Testing single step processing...")
    np.random.seed(42)
    input_pattern = np.random.randint(0, 2, (8, 8), dtype=np.uint8)
    grid_proc = NodeGrid(8, 8, alpha=0.8, beta=0.3, gamma=0.2)
    grid_proc.set_state_from_bits(input_pattern)
    
    initial_state = grid_proc.state.copy()
    grid_proc.step()
    final_state = grid_proc.state.copy()
    
    state_changed = not np.allclose(initial_state, final_state)
    print(f"   ✓ State changed after step: {state_changed}")
    print(f"   ✓ State range: [{np.min(final_state):.3f}, {np.max(final_state):.3f}]")
    
    # Test 5: BlockProcessor
    print("\n5. Testing BlockProcessor...")
    processor = BlockProcessor(grid_proc)
    output, metrics = processor.process_block(input_pattern, steps=2)
    
    print(f"   ✓ Processing time: {metrics['processing_time']:.6f}s")
    print(f"   ✓ Memory delta: {metrics['memory_delta']:,} bytes")
    print(f"   ✓ Output shape: {output.shape}")
    print(f"   ✓ Output bits range: [{np.min(output)}, {np.max(output)}]")
    
    return True

def test_mathematical_transforms():
    """Test mathematical transforms."""
    print("\n=== Testing Mathematical Transforms ===")
    
    # Test DFT
    print("\n1. Testing 2D DFT...")
    test_signal = np.random.randn(8, 8)
    dft_result = dft2(test_signal)
    idft_result = np.real(idft2(dft_result))
    dft_error = np.mean(np.abs(test_signal - idft_result))
    print(f"   ✓ DFT round-trip error: {dft_error:.2e}")
    
    # Test WHT
    print("\n2. Testing Walsh-Hadamard Transform...")
    test_vector = np.random.randn(8)
    wht_result = wht(test_vector)
    iwht_result = iwht(wht_result) / len(test_vector)
    wht_error = np.mean(np.abs(test_vector - iwht_result))
    print(f"   ✓ WHT round-trip error: {wht_error:.2e}")
    
    # Test random orthonormal matrix
    print("\n3. Testing random orthonormal matrix...")
    Q = random_orthonormal(4, seed=42)
    identity_test = Q @ Q.T
    orthogonal_error = np.mean(np.abs(identity_test - np.eye(4)))
    print(f"   ✓ Orthogonality error: {orthogonal_error:.2e}")
    
    return True

def test_performance_scaling():
    """Test performance with different sizes."""
    print("\n=== Testing Performance Scaling ===")
    
    sizes = [16, 32, 64]
    for size in sizes:
        print(f"\nTesting {size}x{size} grid:")
        
        np.random.seed(42)
        grid = NodeGrid(size, size)
        processor = BlockProcessor(grid)
        
        test_data = np.random.randint(0, 2, (size, size), dtype=np.uint8)
        
        # Time the processing
        import time
        start_time = time.perf_counter()
        output, metrics = processor.process_block(test_data, steps=1)
        end_time = time.perf_counter()
        
        processing_time = end_time - start_time
        throughput = (size * size) / processing_time
        
        print(f"   ✓ Processing time: {processing_time:.6f}s")
        print(f"   ✓ Throughput: {throughput:.0f} bits/sec")
        print(f"   ✓ Memory delta: {metrics['memory_delta']:,} bytes")
    
    return True

def main():
    """Run all tests."""
    print("Universal Mathematical Processor - Test Suite")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_mathematical_transforms,
        test_performance_scaling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"\n✓ {test_func.__name__} PASSED")
            else:
                print(f"\n✗ {test_func.__name__} FAILED")
        except Exception as e:
            print(f"\n✗ {test_func.__name__} ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! UMP is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
