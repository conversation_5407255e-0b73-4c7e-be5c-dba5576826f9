#!/usr/bin/env python3
"""
Example usage of Universal Mathematical Processor
Demonstrates basic operations and practical applications.
"""

import numpy as np
import time

# Import UMP classes and functions
exec(open('ump.py', encoding='utf-8').read())

def example_basic_processing():
    """Basic example of UMP processing."""
    print("=== Example 1: Basic Processing ===")
    
    # Create a simple pattern
    np.random.seed(42)
    pattern = np.zeros((16, 16), dtype=np.uint8)
    pattern[4:12, 4:12] = 1  # Square in center
    pattern[6:10, 6:10] = 0  # Hole in square
    
    print("Input pattern: 16x16 square with hole")
    print("Pattern sum:", np.sum(pattern))
    
    # Create processor with specific parameters
    grid = NodeGrid(16, 16, alpha=0.9, beta=0.3, gamma=0.1, theta=0.0)
    processor = BlockProcessor(grid)
    
    # Process the pattern
    output, metrics = processor.process_block(pattern, steps=3)
    
    print(f"Output sum: {np.sum(output)}")
    print(f"Processing time: {metrics['processing_time']:.6f}s")
    print(f"Memory used: {metrics['memory_delta']:,} bytes")
    
    # Compare input vs output
    if 'accuracy' in metrics:
        print(f"Accuracy: {metrics['accuracy']:.3f}")
    else:
        accuracy_val = accuracy(output, pattern)
        print(f"Accuracy: {accuracy_val:.3f}")
    
    return output

def example_edge_detection():
    """Example of edge detection using custom kernels."""
    print("\n=== Example 2: Edge Detection ===")
    
    # Create test image with shapes
    img = np.zeros((32, 32), dtype=np.uint8)
    
    # Add rectangle
    img[8:16, 10:22] = 1
    
    # Add circle
    center_y, center_x = 24, 24
    radius = 6
    y, x = np.ogrid[:32, :32]
    mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
    img[mask] = 1
    
    print("Test image: Rectangle + Circle")
    print("Original image sum:", np.sum(img))
    
    # Sobel edge detection kernel
    sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=np.float64)
    
    # Create grid with edge detection kernel
    grid = NodeGrid(32, 32, alpha=0.1, beta=1.0, gamma=0.0, theta=0.1)
    grid.set_kernel(sobel_x)
    
    processor = BlockProcessor(grid)
    edges, metrics = processor.process_block(img, steps=1)
    
    edge_strength = np.sum(edges) / edges.size
    print(f"Edge strength: {edge_strength:.3f}")
    print(f"Processing time: {metrics['processing_time']:.6f}s")
    
    return edges

def example_transforms():
    """Example of mathematical transforms."""
    print("\n=== Example 3: Mathematical Transforms ===")
    
    # Create a 2D sinusoidal pattern
    t = np.linspace(0, 1, 16)
    x, y = np.meshgrid(t, t)
    signal = np.sin(2*np.pi*2*x) * np.cos(2*np.pi*3*y)
    signal_binary = (signal > 0).astype(np.uint8)
    
    print("Test signal: 2D sinusoidal pattern")
    print("Signal sum:", np.sum(signal_binary))
    
    # Apply different transforms
    transforms = {
        'DFT': lambda x: np.abs(dft2(x.astype(np.float64))),
        'WHT': lambda x: np.abs(wht2d(x.astype(np.float64))),
    }
    
    results = {}
    for name, transform_func in transforms.items():
        start_time = time.perf_counter()
        transformed = transform_func(signal_binary)
        end_time = time.perf_counter()
        
        energy = np.sum(transformed**2)
        max_coeff = np.max(transformed)
        
        print(f"{name:3s}: energy={energy:.2e}, max_coeff={max_coeff:.3f}, time={end_time-start_time:.6f}s")
        results[name] = transformed
    
    return results

def example_performance_comparison():
    """Example comparing different processing parameters."""
    print("\n=== Example 4: Parameter Comparison ===")
    
    # Test data
    np.random.seed(42)
    test_data = np.random.randint(0, 2, (32, 32), dtype=np.uint8)
    
    # Different parameter sets
    param_sets = [
        {'alpha': 1.0, 'beta': 0.0, 'gamma': 0.0, 'name': 'Identity'},
        {'alpha': 0.9, 'beta': 0.1, 'gamma': 0.0, 'name': 'Light smoothing'},
        {'alpha': 0.5, 'beta': 0.5, 'gamma': 0.0, 'name': 'Heavy smoothing'},
        {'alpha': 0.8, 'beta': 0.1, 'gamma': 0.1, 'name': 'With input'},
    ]
    
    print("Comparing different parameter sets:")
    print("Input data sum:", np.sum(test_data))
    
    for params in param_sets:
        grid = NodeGrid(32, 32, 
                       alpha=params['alpha'], 
                       beta=params['beta'], 
                       gamma=params['gamma'])
        processor = BlockProcessor(grid)
        
        output, metrics = processor.process_block(test_data, steps=2, reference=test_data)
        
        print(f"{params['name']:15s}: "
              f"accuracy={metrics.get('accuracy', 0):.3f}, "
              f"time={metrics['processing_time']:.6f}s, "
              f"output_sum={np.sum(output)}")

def example_adaptive_processing():
    """Example of adaptive parameter optimization."""
    print("\n=== Example 5: Adaptive Processing ===")
    
    # Create processor
    grid = NodeGrid(16, 16, alpha=0.8, beta=0.5, gamma=0.2)
    processor = BlockProcessor(grid)
    
    # Generate sequence of test patterns
    np.random.seed(42)
    patterns = []
    references = []
    
    for i in range(5):
        pattern = np.random.randint(0, 2, (16, 16), dtype=np.uint8)
        # Reference is slightly modified pattern (simulating desired output)
        reference = pattern.copy()
        if i % 2 == 0:
            reference = 1 - reference  # Invert every other pattern
        
        patterns.append(pattern)
        references.append(reference)
    
    print("Processing sequence with adaptation:")
    print(f"Initial parameters: α={grid.alpha:.3f}, β={grid.beta:.3f}, γ={grid.gamma:.3f}")
    
    for i, (pattern, reference) in enumerate(zip(patterns, references)):
        output, metrics = processor.process_block(pattern, steps=2, reference=reference)
        
        # Add cost to metrics for adaptation
        cost = processor.compute_cost(reference, output, 
                                    metrics['processing_time'], 
                                    metrics['memory_delta'])
        metrics['cost'] = cost
        processor.history.append(metrics)
        
        print(f"Step {i+1}: accuracy={metrics['accuracy']:.3f}, cost={cost:.6f}")
        
        # Adapt parameters after collecting some history
        if len(processor.history) >= 3:
            processor.adapt_parameters(processor.history)
            print(f"        → α={grid.alpha:.3f}, β={grid.beta:.3f}, γ={grid.gamma:.3f}")

def main():
    """Run all examples."""
    print("Universal Mathematical Processor - Usage Examples")
    print("=" * 60)
    
    examples = [
        example_basic_processing,
        example_edge_detection,
        example_transforms,
        example_performance_comparison,
        example_adaptive_processing
    ]
    
    for example_func in examples:
        try:
            example_func()
        except Exception as e:
            print(f"Error in {example_func.__name__}: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print("\nKey takeaways:")
    print("• UMP can process binary patterns with configurable parameters")
    print("• Different kernels enable various operations (smoothing, edge detection)")
    print("• Mathematical transforms work with the continuous state representation")
    print("• Parameters can be adapted online based on performance metrics")
    print("• Processing is efficient and scales well with problem size")

if __name__ == "__main__":
    main()
