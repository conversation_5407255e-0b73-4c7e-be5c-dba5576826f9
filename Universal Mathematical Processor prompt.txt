````
Zaimplementuj „Universal Mathematical Processor” — wirtualny podzespół wspomagający CPU (Python)

Cel:
Na<PERSON>z kompletny, testowalny i rozszerzalny pakiet Pythona o nazwie `umprocessor`, który emuluje wirtualny podzespół matematyczny. <PERSON><PERSON><PERSON> bit jest reprezentowany jako węzeł (node) z ciągłym stanem. System musi realizować zadane wzory matematyczne, interfejsy i testy tak, aby dowolne inne AI mogło wygenerować identyczny kod na podstawie tego promptu.

Wymagania wysokiego poziomu:
1. Język: Python 3.10+.
2. Biblioteki: numpy, scipy, numba (opcjonalnie), pytest, pytest-benchmark. Kod ma działać bez GPU, ale ma miejsca do przyspieszenia (numba/cupy/jax).
3. Struktura repo:
   - umprocessor/
     - __init__.py
     - node.py          # Node, NodeGrid
     - processor.py     # BlockProcessor, optymalizacja, adaptacja
     - transforms.py    # FFT, WHT, losowe ortonormalne
     - utils.py         # kod korekcji, metryki, I/O
     - tests/
       - test_basic.py
       - test_transforms.py
       - test_performance.py
     - benchmarks/
       - bench_basic.py
   - README.md
   - setup.cfg / pyproject.toml
   - LICENSE (MIT)

Matematyczna specyfikacja (dokładnie zaimplementować):

1) Reprezentacja stanu:
   - Dla każdego węzła i indeksu i przechowuj skalarny stan rzeczywisty s_i(t) ∈ R lub zespolony c_i(t) ∈ C.
   - Mapowanie na bit:
     b_i(t) = 1  ⇔  Re(c_i(t)) > θ, w przeciwnym razie 0.
   - Parametr progowy θ konfigurowalny globalnie lub per-node.

2) Reguła lokalnej aktualizacji (dla pojedynczego węzła):
   s_i^{t+1} = σ( α s_i^t + β ∑_{j∈N(i)} w_{ij} g(s_j^t) + γ I_i^t )
   gdzie:
   - σ(x) = tanh(x) lub inna nieliniowość konfigurowalna.
   - g(·) = transformacja sąsiednia (np. identyczność, nieliniowa funkcja, DFT/IDFT po bloku).
   - w_{ij} = wagi sąsiedztwa (kernel 2D K lub macierz rzadką W).
   - I_i^t = sygnał wejściowy (np. bit wejściowy z CPU).
   - α, β, γ są parametrami skalarowymi.

3) Wersja wektorowa (macierz 2D H×W):
   S^{t+1} = σ( α S^t + β (K * g(S^t)) + γ I^t )
   - * to konwolucja dyskretna 2D; jeśli K ma mały rozmiar użyć scipy.signal.convolve2d lub mnożenia w przestrzeni FFT:
     K * g(S) = Re( ifft2( fft2(g(S)) * fft2(K_padded) ) )

4) Transformacje grupowe (równoległe):
   - Dla bloku v∈R^m stosuj v' = T v, gdzie T ∈ {DFT, WHT, Q}:
     - DFT: np.fft.fft / np.fft.ifft
     - WHT: implementacja Walsh–Hadamard dla m będącego potęgą 2 (algorytm iteracyjny)
     - Q: losowa ortonormalna macierz z QR (fix seed dla powtarzalności)
   - Po transformacji możliwa jest operacja próbkowania, kwantyzacji lub progowania.

5) Redukcja do decyzji (binarny wynik):
   b_i = 1 ⇔ Re(∑_k ω_k c_{i,k}) > θ
   - ω_k to znormalizowane wagi agregacji (domyślnie ω = 1 dla pojedynczego stanu).

6) Metryki i koszt J do optymalizacji:
   - Błąd logiczny E = (# bitów różniących się od wzorca referencyjnego) / N
   - Czas T = zmierzony czas operacji
   - Pamięć M = pamięć użyta w bajtach
   - J = λ_err E + λ_time T + λ_mem M
   - λ_* konfigurowalne.

7) Adaptacja parametrów (online):
   - Minimalizacja J przez SGD/Adam na oknach danych.
   - Aktualizowane parametry: α, β, γ, θ, ew. wagi K.
   - Pseudokod Adam (zaimplementować zgodnie z Kingma & Ba).

8) Grupowanie / optymalizacja blokowa:
   - Twórz grupy przez spektralne grupowanie na macierzy korelacji C_{ij} = corr(s_i, s_j).
   - Dla każdej grupy stosuj transformację blokową (T) i przetwarzaj równolegle.

9) Kod korekcji błędów (opcjonalne rozszerzenie):
   - Prosty blok parzystości: dodaj bit parzystości do bloku i implementuj detekcję błędów.
   - Przygotować API do podłączenia LDPC/BCH (implementacja zostanie minimalna, punkt wejściowy).

Wymagania implementacyjne i API:

A) Klasy i funkcje:
   - class NodeGrid:
       - __init__(H, W, dtype=np.complex128, K=None, alpha=1.0, beta=1.0, gamma=1.0, theta=0.0, sigma=np.tanh)
       - state : numpy.ndarray shape (H,W) dtype
       - step(I=None) -> None  # aktualizuje state zgodnie z regułą
       - to_bits() -> np.ndarray shape (H,W) dtype=np.uint8
       - set_kernel(K)  # K small np.ndarray
   - class BlockProcessor:
       - process_block(block_in: np.ndarray, steps:int=1, reference:np.ndarray|None=None) -> (block_out, metrics)
       - adapt_parameters(window_data: list) -> None
       - compute_cost(reference, output) -> float
   - transforms.py:
       - dft2, idft2, wht, iwht, random_orthonormal
   - utils.py:
       - metrics: accuracy, E, time, memory_estimate
       - parity_check functions
   - tests: użyj deterministycznych seedów.

B) Implementacja wzoru w Pythonie (fragment do implementacji dokładnie tak):
   ```python
   import numpy as np
   from scipy.signal import convolve2d
   def update_block(S, K, alpha, beta, gamma, I, sigma=np.tanh, g=lambda x: x):
       # S, I: np.ndarray (H,W)
       GS = g(S)                               # transformacja sąsiednia
       conv = convolve2d(GS, K, mode='same', boundary='wrap')
       S_next = sigma(alpha * S + beta * conv + gamma * I)
       return S_next
````

* Alternatywnie przy dużych blokach użyć FFT:

```python
def conv_fft(GS, K, shape):
    FK = np.fft.fft2(K, s=shape)
    FG = np.fft.fft2(GS)
    return np.real(np.fft.ifft2(FG * FK))
```

C) Testy jednostkowe minimalne:

* Test 1: deterministyczny input 8x8 z seed=0; porównaj wynik process\_block z klasyczną operacją bitową (np. input AND mask) — oblicz accuracy >= zadany próg (np. 0.90) lub pokaż różnice.
* Test 2: walidacja transformacji DFT i WHT (różne rozmiary).
* Test 3: benchmarking: zmierz czas dla rozmiarów 32x32, 128x128, 512x512 i zapisz czasy.

D) Przykład użycia (w README):

* Generuj losowy blok bitów:

```python
import numpy as np
from umprocessor import NodeGrid, BlockProcessor
np.random.seed(0)
block = np.random.randint(0,2,(64,64)).astype(np.uint8)
grid = NodeGrid(64,64, alpha=0.9, beta=0.5, gamma=0.1, theta=0.0)
proc = BlockProcessor(grid)
out, metrics = proc.process_block(block, steps=4)
bits_out = grid.to_bits()
```

* W README zamieścić interpretację metryk.

E) Wskazówki wydajnościowe:

* Wektoryzacja: żadnych pętli po komórkach w implementacji głównej.
* Dla małych K użyć convolve2d z mode='same', boundary='wrap'.
* Dla dużych rozmiarów użyć FFT-konwolucji.
* Opcjonalne przyspieszenie: dekoratory numba.njit dla funkcji bottleneck, lub zewnętrzne backendy.

F) Dodatkowe wymagania jakości:

* Typy danych i adnotacje PEP 484.
* Dokładne docstringi z wzorami matematycznymi (LaTeX lub plain).
* Testy muszą być deterministyczne (ustaw seed).
* CLI do uruchomienia testów i benchmarków.
* Plik konfiguracyjny z domyślnymi parametrami (alpha,beta,gamma,theta,K).

G) Walidacja koncepcji:

* Dołącz skrypt `validate_accuracy.py` który porównuje rezultaty `umprocessor` z klasyczną logiką bitową dla:

  * operacji AND, OR, XOR za pomocą odpowiednich dobrych wyborów parametrów (np. ustaw g identyfikator, θ=0.5 i skalowanie).
* Wynik raportu: accuracy, J oraz profil czasu.

H) Deliverable (co AI ma wygenerować):

* Pełne pliki źródłowe zgodne z powyższą strukturą.
* README z instrukcją instalacji i użycia oraz krótkim opisem matematyki.
* Testy pytest i benchmarki.
* Krótkie demo w notebooku lub skrypcie uruchamiającym przykład.

Słowa końcowe do AI wykonującego zadanie:

* Implementuj dokładnie podane wzory i pseudokod.
* Dbaj o deterministyczność testów.
* Oznacz miejsca do optymalizacji numba/cupy.
* Komentarze techniczne wyjaśniające mapping wzoru → kod.
* Nie dodawaj features wykraczających poza specyfikację bez wyraźnego oznaczenia jako „opcjonalne”.

Przykładowe wartości domyślne (do testów):

* H=W=64, K = \[\[0.05,0.1,0.05],\[0.1,0.4,0.1],\[0.05,0.1,0.05]], alpha=0.9, beta=0.7, gamma=0.1, theta=0.0, sigma=tanh.

```
